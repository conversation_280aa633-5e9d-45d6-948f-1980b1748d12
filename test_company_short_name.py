#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试公司简称提取和AI分析功能
"""

import sys
import os
sys.path.append('docker-deploy')

def test_company_short_name_extraction():
    """测试公司简称提取功能"""
    print("🧪 测试公司简称提取功能")
    print("=" * 60)
    
    try:
        from web_spider import extract_company_short_name
        
        # 用户提供的测试数据
        test_cases = [
            "常熟世名化工科技有限公司",
            "苏州汇彩新材料科技有限公司", 
            "昆山世盈资本管理有限公司",
            "苏州世润新材料科技有限公司",
            "苏州世名彩捷科技有限公司",
            "上海芯彩企业管理有限公司",
            "岳阳凯门水性助剂有限公司",
            "海南丹彩科技有限公司",
            "岳阳凯门新材料有限公司",
            "世名(苏州)新材料研究院有限公司",
            "世名(辽宁)新材料有限公司",
            "世名新能源科技(苏州)有限公司",
            "浙江上嘉色彩科技有限公司",
            "江苏锋晖新能源发展有限公司"
        ]
        
        print("📝 测试公司简称提取:")
        for i, company_name in enumerate(test_cases, 1):
            short_name = extract_company_short_name(company_name)
            print(f"{i:2d}. {company_name} -> {short_name}")
        
        print("\n" + "=" * 60)
        print("✅ 公司简称提取测试完成")
        
        return test_cases
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_ai_analysis_with_short_names():
    """测试AI分析中的公司简称功能"""
    print("\n🧪 测试AI分析中的公司简称功能")
    print("=" * 60)

    try:
        from app import find_related_party_contexts, remove_duplicate_contexts_global

        # 创建测试文本，包含完整公司名称和简称，故意制造重复内容
        test_text = """
        公司与常熟世名化工科技有限公司建立了长期合作关系，在新材料研发方面开展深度合作。
        世名化工作为我们的重要供应商，提供高质量的化工原料。
        苏州汇彩新材料科技有限公司是我们的战略合作伙伴，汇彩新材料在色彩技术方面具有领先优势。
        与世名(苏州)新材料研究院有限公司共同开展技术创新项目，世名研究院在新材料领域有丰富经验。
        岳阳凯门水性助剂有限公司为我们提供环保助剂产品，凯门助剂的产品质量稳定可靠。

        在另一个章节中，我们再次提到常熟世名化工科技有限公司建立了长期合作关系，在新材料研发方面开展深度合作。
        世名化工在行业中具有重要地位，是我们信赖的合作伙伴。
        """

        # 测试关键词
        test_keywords = ["合作", "供应商", "技术", "创新", "新材料"]

        # 测试关联方（使用完整公司名称）
        test_related_parties = [
            "常熟世名化工科技有限公司",
            "苏州汇彩新材料科技有限公司",
            "世名(苏州)新材料研究院有限公司",
            "岳阳凯门水性助剂有限公司"
        ]
        
        print("📝 测试文本:")
        print(test_text)
        print(f"\n🔍 测试关键词: {test_keywords}")
        print(f"\n👥 测试关联方: {test_related_parties}")
        print("\n" + "=" * 60)
        
        # 测试每个关联方的上下文提取
        all_contexts = []
        for i, related_party in enumerate(test_related_parties, 1):
            print(f"\n📄 测试关联方 {i}: {related_party}")
            print("-" * 50)

            party_contexts = find_related_party_contexts(test_text, related_party, test_keywords)

            print(f"找到 {len(party_contexts)} 个相关上下文:")
            for j, context_info in enumerate(party_contexts, 1):
                print(f"\n  上下文 {j}:")
                print(f"    搜索词: {context_info.get('search_term', '未知')}")
                print(f"    是否简称: {context_info.get('is_short_name', False)}")
                print(f"    包含关键词: {context_info['has_keywords']}")
                print(f"    找到的关键词: {context_info['keywords_found']}")
                print(f"    文本片段: {context_info['text'][:150]}...")

                # 模拟AI分析接口的数据结构
                all_contexts.append({
                    'stock_code': '000001',
                    'company_name': '测试公司',
                    'related_party': related_party,
                    'context': context_info['text'],
                    'keywords_found': context_info['keywords_found'],
                    'has_keywords': context_info['has_keywords'],
                    'context_type': 'with_keywords' if context_info['has_keywords'] else 'related_party_only',
                    'search_term': context_info.get('search_term', related_party),
                    'is_short_name': context_info.get('is_short_name', False)
                })

        # 测试全局去重功能
        print(f"\n🔄 测试全局去重功能")
        print("-" * 50)
        print(f"去重前总上下文数量: {len(all_contexts)}")

        unique_contexts = remove_duplicate_contexts_global(all_contexts)
        print(f"去重后总上下文数量: {len(unique_contexts)}")

        print(f"\n📊 去重后的上下文列表:")
        for i, ctx in enumerate(unique_contexts, 1):
            print(f"  {i}. 关联方: {ctx['related_party']}")
            print(f"     搜索词: {ctx.get('search_term', '未知')}")
            print(f"     类型: {ctx['context_type']}")
            print(f"     关键词: {ctx['keywords_found']}")
            print(f"     文本: {ctx['context'][:100]}...")
            print()

        print("\n" + "=" * 60)
        print("✅ AI分析公司简称功能和去重测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🚀 开始测试公司简称提取和AI分析功能")
    print("=" * 80)
    
    # 测试公司简称提取
    test_cases = test_company_short_name_extraction()
    
    # 测试AI分析功能
    if test_cases:
        test_ai_analysis_with_short_names()
    
    print("\n" + "=" * 80)
    print("🎉 所有测试完成")

if __name__ == "__main__":
    main()
