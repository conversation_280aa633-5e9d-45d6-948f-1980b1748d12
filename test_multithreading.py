#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多线程上下文处理功能
"""

import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor
sys.path.append('docker-deploy')

def test_multithreading_performance():
    """测试多线程性能"""
    print("🧪 测试多线程上下文处理性能")
    print("=" * 60)
    
    try:
        # 模拟年报数据
        mock_reports = []
        for i in range(5):
            mock_reports.append({
                'stock_code': f'00000{i+1}',
                'company_name': f'测试公司{i+1}',
                'txt_content': f"""
                这是测试公司{i+1}的年报内容。
                公司与常熟世名化工科技有限公司建立了长期合作关系，在新材料研发方面开展深度合作。
                世名化工作为我们的重要供应商，提供高质量的化工原料。
                苏州汇彩新材料科技有限公司是我们的战略合作伙伴，汇彩新材料在色彩技术方面具有领先优势。
                与世名(苏州)新材料研究院有限公司共同开展技术创新项目，世名研究院在新材料领域有丰富经验。
                岳阳凯门水性助剂有限公司为我们提供环保助剂产品，凯门助剂的产品质量稳定可靠。
                公司在技术创新方面投入巨大，与多家供应商建立了合作关系。
                在新材料领域，我们与合作伙伴共同推进产业发展。
                """ * 10  # 重复10次增加文本长度
            })
        
        # 测试参数
        keywords = ["合作", "供应商", "技术", "创新", "新材料"]
        related_parties = [
            "常熟世名化工科技有限公司",
            "苏州汇彩新材料科技有限公司",
            "世名(苏州)新材料研究院有限公司",
            "岳阳凯门水性助剂有限公司"
        ]
        
        print(f"📊 测试数据:")
        print(f"  年报数量: {len(mock_reports)}")
        print(f"  关键词数量: {len(keywords)}")
        print(f"  关联方数量: {len(related_parties)}")
        print(f"  每个年报文本长度: ~{len(mock_reports[0]['txt_content'])} 字符")
        
        # 测试单线程处理
        print(f"\n🔄 测试单线程处理...")
        start_time = time.time()
        single_contexts = test_single_threaded_processing(mock_reports, keywords, related_parties)
        single_time = time.time() - start_time
        print(f"  单线程耗时: {single_time:.2f} 秒")
        print(f"  找到上下文: {len(single_contexts)} 个")
        
        # 测试多线程处理
        print(f"\n🔀 测试多线程处理...")
        start_time = time.time()
        multi_contexts = test_multi_threaded_processing(mock_reports, keywords, related_parties)
        multi_time = time.time() - start_time
        print(f"  多线程耗时: {multi_time:.2f} 秒")
        print(f"  找到上下文: {len(multi_contexts)} 个")
        
        # 性能对比
        if single_time > 0:
            speedup = single_time / multi_time
            print(f"\n📈 性能对比:")
            print(f"  加速比: {speedup:.2f}x")
            print(f"  时间节省: {((single_time - multi_time) / single_time * 100):.1f}%")
        
        # 验证结果一致性
        print(f"\n🔍 验证结果一致性:")
        print(f"  单线程结果数量: {len(single_contexts)}")
        print(f"  多线程结果数量: {len(multi_contexts)}")
        
        if len(single_contexts) == len(multi_contexts):
            print("  ✅ 结果数量一致")
        else:
            print("  ❌ 结果数量不一致")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_threaded_processing(reports, keywords, related_parties):
    """模拟单线程处理"""
    contexts = []
    
    for report in reports:
        # 模拟上下文提取过程
        time.sleep(0.1)  # 模拟处理时间
        
        for related_party in related_parties:
            # 模拟找到的上下文
            contexts.append({
                'stock_code': report['stock_code'],
                'company_name': report['company_name'],
                'related_party': related_party,
                'context': f"模拟上下文: {related_party} 在 {report['company_name']} 的年报中",
                'keywords_found': keywords[:2],  # 模拟找到前2个关键词
                'has_keywords': True,
                'context_type': 'with_keywords'
            })
    
    return contexts

def test_multi_threaded_processing(reports, keywords, related_parties):
    """模拟多线程处理"""
    contexts = []
    
    def process_report(report):
        # 模拟上下文提取过程
        time.sleep(0.1)  # 模拟处理时间
        
        report_contexts = []
        for related_party in related_parties:
            # 模拟找到的上下文
            report_contexts.append({
                'stock_code': report['stock_code'],
                'company_name': report['company_name'],
                'related_party': related_party,
                'context': f"模拟上下文: {related_party} 在 {report['company_name']} 的年报中",
                'keywords_found': keywords[:2],  # 模拟找到前2个关键词
                'has_keywords': True,
                'context_type': 'with_keywords'
            })
        return report_contexts
    
    # 使用线程池
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(process_report, report) for report in reports]
        
        for future in futures:
            report_contexts = future.result()
            contexts.extend(report_contexts)
    
    return contexts

def test_thread_safety():
    """测试线程安全性"""
    print("\n🧪 测试线程安全性")
    print("=" * 60)
    
    shared_data = {'counter': 0, 'contexts': []}
    lock = threading.Lock()
    
    def worker_function(worker_id):
        for i in range(100):
            with lock:
                shared_data['counter'] += 1
                shared_data['contexts'].append(f"Worker-{worker_id}-Context-{i}")
            time.sleep(0.001)  # 模拟处理时间
    
    # 创建多个线程
    threads = []
    for i in range(5):
        thread = threading.Thread(target=worker_function, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print(f"  最终计数器值: {shared_data['counter']}")
    print(f"  上下文数量: {len(shared_data['contexts'])}")
    print(f"  预期值: 500")
    
    if shared_data['counter'] == 500 and len(shared_data['contexts']) == 500:
        print("  ✅ 线程安全测试通过")
        return True
    else:
        print("  ❌ 线程安全测试失败")
        return False

def main():
    """主测试函数"""
    print("🚀 开始多线程功能测试")
    print("=" * 80)
    
    # 测试性能
    success1 = test_multithreading_performance()
    
    # 测试线程安全
    success2 = test_thread_safety()
    
    print("\n" + "=" * 80)
    if success1 and success2:
        print("🎉 所有多线程测试通过")
        print("\n💡 建议:")
        print("  - 对于少量年报（<2个），使用单线程处理")
        print("  - 对于大量年报（>=2个），使用多线程处理")
        print("  - 线程数建议设置为 CPU核心数 * 2")
        print("  - 注意内存使用，避免创建过多线程")
    else:
        print("❌ 部分测试失败")

if __name__ == "__main__":
    main()
