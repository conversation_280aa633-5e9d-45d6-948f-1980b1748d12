import re

def extract_short_name(full_name):
    """
    提取公司简称的多种策略
    """
    # 策略1: 去除常见的公司类型后缀和地名前缀
    # 常见公司类型后缀
    company_suffixes = [
        '有限公司', '股份有限公司', '集团有限公司', '控股有限公司',
        '投资有限公司', '科技有限公司', '实业有限公司', '贸易有限公司',
        '工业有限公司', '发展有限公司', '建设有限公司', '管理有限公司',
        '咨询有限公司', '服务有限公司', '技术有限公司', '设备有限公司',
        '材料有限公司', '制造有限公司', '生产有限公司', '销售有限公司',
        '新材料研究院有限公司', '研究院有限公司', '新材料有限公司',
        '有限责任公司', '股份公司', '集团公司', '控股公司',
        '投资公司', '科技公司', '实业公司', '贸易公司',
        '工业公司', '发展公司', '建设公司', '管理公司',
        '咨询公司', '服务公司', '技术公司', '设备公司',
        '材料公司', '制造公司', '生产公司', '销售公司',
        '公司', '集团', '企业', '厂', '所', '院', '中心'
    ]

    # 常见地名前缀（省市县区名）
    location_prefixes = [
        '北京', '上海', '天津', '重庆', '广东', '江苏', '浙江', '山东', '河南', '四川',
        '湖北', '湖南', '河北', '福建', '安徽', '陕西', '辽宁', '山西', '江西', '黑龙江',
        '吉林', '云南', '贵州', '广西', '海南', '甘肃', '青海', '宁夏', '新疆', '西藏',
        '内蒙古', '深圳', '广州', '杭州', '南京', '苏州', '成都', '武汉', '西安', '郑州',
        '青岛', '大连', '宁波', '厦门', '长沙', '哈尔滨', '沈阳', '长春', '石家庄',
        '济南', '南昌', '福州', '合肥', '太原', '昆明', '贵阳', '南宁', '海口', '兰州',
        '西宁', '银川', '乌鲁木齐', '拉萨', '呼和浩特', '常熟', '无锡', '徐州', '常州',
        '扬州', '南通', '连云港', '淮安', '盐城', '镇江', '泰州', '宿迁', '温州', '嘉兴',
        '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水', '佛山', '东莞', '中山',
        '珠海', '汕头', '韶关', '河源', '梅州', '惠州', '汕尾', '阳江', '湛江', '茂名',
        '肇庆', '清远', '潮州', '揭阳', '云浮', '昆山', '岳阳'
    ]

    # 去除前后空白
    name = full_name.strip()

    # 特殊处理：先移除括号内的地名信息
    # 例如：世名(苏州)新材料研究院有限公司 -> 世名新材料研究院有限公司
    name = re.sub(r'\([^)]*\)', '', name)

    # 策略1: 去除地名前缀
    for prefix in location_prefixes:
        if name.startswith(prefix):
            name = name[len(prefix):]
            break

    # 策略2: 去除公司类型后缀（按长度排序，先匹配长的）
    company_suffixes.sort(key=len, reverse=True)
    for suffix in company_suffixes:
        if name.endswith(suffix):
            name = name[:-len(suffix)]
            break

    # 策略3: 如果处理后的名称太长，尝试提取核心词汇
    if len(name) > 6:
        # 尝试匹配常见的核心业务词汇模式
        # 例如：XX化工、XX科技、XX实业等
        business_patterns = [
            r'(\w{2,4})(化工|科技|实业|贸易|投资|建设|发展|制造|材料|设备|技术|服务|管理)',
            r'(\w{2,4})(集团|控股|股份)',
            r'(\w{2,4})(电子|机械|医药|食品|纺织|钢铁|有色|煤炭|石油|电力|交通|房地产)',
            r'(\w{2,4})(新能源|新材料|水性助剂|企业管理|资本管理|色彩科技)',
        ]

        for pattern in business_patterns:
            match = re.search(pattern, name)
            if match:
                return match.group(1) + match.group(2)

    # 策略4: 如果名称仍然较长，取前4个字符
    if len(name) > 4:
        name = name[:4]

    # 如果处理后为空或太短，返回原名称的前几个字符
    if len(name) < 2:
        return full_name[:min(4, len(full_name))]

    return name

def extract_short_name_simple(full_name):
    """
    简单版本：主要去除地名和公司后缀
    """
    name = full_name.strip()

    # 先移除括号内容
    name = re.sub(r'\([^)]*\)', '', name)

    # 去除常见地名前缀
    name = re.sub(r'^(北京|上海|天津|重庆|深圳|广州|杭州|南京|苏州|成都|武汉|西安|郑州|常熟|无锡|温州|佛山|东莞|昆山|岳阳|浙江|江苏|海南)', '', name)

    # 去除常见公司后缀（按长度排序，先匹配长的）
    suffixes = [
        '新材料研究院有限公司', '研究院有限公司', '新材料有限公司', '科技有限公司',
        '有限公司', '股份有限公司', '集团有限公司', '有限责任公司',
        '股份公司', '集团公司', '科技公司', '公司', '集团', '企业'
    ]

    for suffix in suffixes:
        if name.endswith(suffix):
            name = name[:-len(suffix)]
            break

    # 如果名称太长，取前4个字符
    if len(name) > 4:
        name = name[:4]

    # 如果处理后为空，返回原名称前4个字符
    if not name:
        name = full_name[:4]

    return name

# 测试函数
def test_extract_functions():
    # 用户提供的真实测试数据
    test_cases = [
        "常熟世名化工科技有限公司",
        "苏州汇彩新材料科技有限公司",
        "昆山世盈资本管理有限公司",
        "苏州世润新材料科技有限公司",
        "苏州世名彩捷科技有限公司",
        "上海芯彩企业管理有限公司",
        "岳阳凯门水性助剂有限公司",
        "海南丹彩科技有限公司",
        "岳阳凯门新材料有限公司",
        "世名(苏州)新材料研究院有限公司",
        "世名(辽宁)新材料有限公司",
        "世名新能源科技(苏州)有限公司",
        "浙江上嘉色彩科技有限公司",
        "江苏锋晖新能源发展有限公司"
    ]

    print("=== 详细版本测试 ===")
    for case in test_cases:
        result = extract_short_name(case)
        print(f"{case} -> {result}")

    print("\n=== 简单版本测试 ===")
    for case in test_cases:
        result = extract_short_name_simple(case)
        print(f"{case} -> {result}")

def process_company_excel(file_path='company.xlsx'):
    """
    处理company.xlsx文件，从A列读取公司全称，生成简称并写入B列
    """
    try:
        import pandas as pd

        # 读取Excel文件
        df = pd.read_excel(file_path)

        # 确保有足够的列
        if df.shape[1] < 2:
            # 如果只有一列，添加第二列
            df['简称'] = ''

        # 获取A列的公司名称（假设第一列是公司全称）
        company_names = df.iloc[:, 0].dropna()  # 去除空值

        print(f"找到 {len(company_names)} 个公司名称")
        print("开始提取简称...")

        # 为每个公司名称生成简称
        short_names = []
        for i, full_name in enumerate(company_names):
            if pd.isna(full_name) or str(full_name).strip() == '':
                short_names.append('')
                continue

            # 使用extract_short_name函数生成简称
            short_name = extract_short_name(str(full_name))
            short_names.append(short_name)

            print(f"{i+1:3d}. {full_name} -> {short_name}")

        # 将简称写入B列
        # 先确保B列有足够的行数
        while len(df) < len(short_names):
            df.loc[len(df)] = ['', '']

        # 填充B列
        for i, short_name in enumerate(short_names):
            df.iloc[i, 1] = short_name

        # 设置列名
        df.columns = ['公司全称', '公司简称']

        # 保存回Excel文件
        df.to_excel(file_path, index=False)

        print(f"\n处理完成！已将简称写入 {file_path} 的B列")
        print(f"共处理了 {len(short_names)} 个公司名称")

        return df

    except ImportError:
        print("错误：需要安装pandas库")
        print("请运行：pip install pandas openpyxl")
        return None
    except Exception as e:
        print(f"处理Excel文件时出错：{e}")
        return None

if __name__ == "__main__":
    # 如果直接运行此脚本，处理company.xlsx文件
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        # 运行测试函数
        test_extract_functions()
    else:
        # 处理Excel文件
        process_company_excel()