# AI分析公司简称功能说明

## 功能概述

为AI分析功能添加了智能公司简称提取和重复内容过滤功能，当用户提供关联方公司名称时，系统会自动：

1. **提取公司简称**：从完整公司名称中提取核心简称
2. **双重搜索**：同时搜索完整公司名称和简称的上下文
3. **去重过滤**：移除重复或高度相似的上下文内容
4. **智能标记**：标记上下文来源（完整名称或简称匹配）

## 核心功能

### 1. 公司简称提取算法

#### 处理步骤：
1. **移除括号内容**：如 `世名(苏州)新材料研究院有限公司` → `世名新材料研究院有限公司`
2. **去除地名前缀**：如 `常熟世名化工科技有限公司` → `世名化工科技有限公司`
3. **去除公司后缀**：如 `世名化工科技有限公司` → `世名化工科技`
4. **业务词汇匹配**：如 `世名化工科技` → `世名化工`
5. **长度控制**：超过4个字符时截取前4个字符

#### 测试结果：
```
常熟世名化工科技有限公司 -> 世名化工
苏州汇彩新材料科技有限公司 -> 汇彩新材
昆山世盈资本管理有限公司 -> 世盈资本
苏州世润新材料科技有限公司 -> 世润新材
苏州世名彩捷科技有限公司 -> 世名彩捷
上海芯彩企业管理有限公司 -> 芯彩企业
岳阳凯门水性助剂有限公司 -> 凯门水性
海南丹彩科技有限公司 -> 丹彩
岳阳凯门新材料有限公司 -> 凯门
世名(苏州)新材料研究院有限公司 -> 世名
世名(辽宁)新材料有限公司 -> 世名
世名新能源科技(苏州)有限公司 -> 世名新能
浙江上嘉色彩科技有限公司 -> 上嘉色彩
江苏锋晖新能源发展有限公司 -> 锋晖新能
```

### 2. 双重搜索机制

当用户输入关联方公司名称时，系统会：

1. **生成搜索词列表**：
   - 完整公司名称：`常熟世名化工科技有限公司`
   - 提取的简称：`世名化工`

2. **分别搜索上下文**：
   - 在年报文本中搜索完整名称的出现位置
   - 在年报文本中搜索简称的出现位置

3. **合并结果**：
   - 将两种搜索结果合并
   - 标记每个上下文的来源（完整名称或简称）

### 3. 重复内容过滤

#### 去重策略：
1. **完全相同检测**：移除清理后文本完全相同的上下文
2. **高度相似检测**：
   - 计算较短文本在较长文本中的包含关系
   - 相似度 > 60% 时认为是重复内容
   - 保留更完整或包含更多关键词的版本

#### 去重级别：
1. **单关联方去重**：同一关联方的不同搜索词结果去重
2. **全局去重**：不同关联方之间的重复内容去重

### 4. 智能标记系统

每个上下文都包含以下标记信息：
- `search_term`：实际匹配的搜索词
- `is_short_name`：是否通过简称匹配
- `related_party`：原始关联方名称
- `context_type`：上下文类型（包含关键词/仅关联方名称）

## 使用示例

### 输入：
```
关联方公司：常熟世名化工科技有限公司
关键词：合作, 供应商, 技术, 创新
```

### 处理过程：
1. **简称提取**：`常熟世名化工科技有限公司` → `世名化工`
2. **双重搜索**：
   - 搜索 `常熟世名化工科技有限公司`
   - 搜索 `世名化工`
3. **上下文提取**：提取包含这些词汇的300字符上下文
4. **去重过滤**：移除重复或高度相似的内容
5. **传递给AI**：将去重后的上下文传递给AI分析

### 输出示例：
```
找到 3 个独特的上下文：
1. 关联方: 常熟世名化工科技有限公司 (匹配简称: 世名化工)
   类型: 包含关键词
   关键词: [合作, 技术]
   文本: 公司与世名化工建立了长期技术合作关系...

2. 关联方: 常熟世名化工科技有限公司
   类型: 包含关键词  
   关键词: [供应商]
   文本: 常熟世名化工科技有限公司作为重要供应商...

3. 关联方: 常熟世名化工科技有限公司 (匹配简称: 世名化工)
   类型: 仅关联方名称
   关键词: []
   文本: 世名化工在行业中具有重要地位...
```

## 技术优势

1. **提高召回率**：通过简称搜索找到更多相关内容
2. **减少冗余**：智能去重避免重复信息传递给AI
3. **保持精度**：标记信息帮助AI理解上下文来源
4. **用户友好**：用户只需输入完整公司名称，系统自动处理

## 配置参数

- **相似度阈值**：60%（可调整）
- **上下文长度**：300字符（可调整）
- **简称最小长度**：2个字符
- **简称最大长度**：4个字符

## 文件修改清单

1. **web_spider.py**：
   - 添加 `extract_company_short_name()` 函数
   - 修改 `analyze_related_parties()` 函数支持双重搜索

2. **app.py**：
   - 修改 `find_related_party_contexts()` 函数
   - 添加 `remove_duplicate_contexts()` 函数
   - 添加 `remove_duplicate_contexts_global()` 函数
   - 修改AI分析流式接口支持增强功能

3. **测试文件**：
   - `test_core_functions.py`：核心功能测试
   - `test_company_short_name.py`：完整功能测试

## 使用方法

用户在AI分析界面中：
1. 输入股票代码
2. 输入关联方公司完整名称（如：`常熟世名化工科技有限公司`）
3. 输入关键词
4. 点击开始分析

系统会自动：
- 提取公司简称
- 双重搜索上下文
- 去重过滤
- 传递给AI分析

无需用户额外操作，完全自动化处理。
