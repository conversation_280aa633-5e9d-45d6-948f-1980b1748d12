#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试核心功能：公司简称提取和去重
"""

import sys
import os
import re

# 直接复制核心函数，避免导入整个app模块
def extract_company_short_name(full_name):
    """
    提取公司简称
    """
    # 常见公司类型后缀
    company_suffixes = [
        '新材料研究院有限公司', '研究院有限公司', '新材料有限公司',
        '有限公司', '股份有限公司', '集团有限公司', '控股有限公司',
        '投资有限公司', '科技有限公司', '实业有限公司', '贸易有限公司',
        '工业有限公司', '发展有限公司', '建设有限公司', '管理有限公司',
        '咨询有限公司', '服务有限公司', '技术有限公司', '设备有限公司',
        '材料有限公司', '制造有限公司', '生产有限公司', '销售有限公司',
        '有限责任公司', '股份公司', '集团公司', '控股公司',
        '投资公司', '科技公司', '实业公司', '贸易公司',
        '工业公司', '发展公司', '建设公司', '管理公司',
        '咨询公司', '服务公司', '技术公司', '设备公司',
        '材料公司', '制造公司', '生产公司', '销售公司',
        '公司', '集团', '企业', '厂', '所', '院', '中心'
    ]
    
    # 常见地名前缀
    location_prefixes = [
        '北京', '上海', '天津', '重庆', '广东', '江苏', '浙江', '山东', '河南', '四川',
        '湖北', '湖南', '河北', '福建', '安徽', '陕西', '辽宁', '山西', '江西', '黑龙江',
        '吉林', '云南', '贵州', '广西', '海南', '甘肃', '青海', '宁夏', '新疆', '西藏',
        '内蒙古', '深圳', '广州', '杭州', '南京', '苏州', '成都', '武汉', '西安', '郑州',
        '青岛', '大连', '宁波', '厦门', '长沙', '哈尔滨', '沈阳', '长春', '石家庄',
        '济南', '南昌', '福州', '合肥', '太原', '昆明', '贵阳', '南宁', '海口', '兰州',
        '西宁', '银川', '乌鲁木齐', '拉萨', '呼和浩特', '常熟', '无锡', '徐州', '常州',
        '扬州', '南通', '连云港', '淮安', '盐城', '镇江', '泰州', '宿迁', '温州', '嘉兴',
        '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水', '佛山', '东莞', '中山',
        '珠海', '汕头', '韶关', '河源', '梅州', '惠州', '汕尾', '阳江', '湛江', '茂名',
        '肇庆', '清远', '潮州', '揭阳', '云浮', '昆山', '岳阳'
    ]
    
    # 去除前后空白
    name = full_name.strip()
    
    # 特殊处理：先移除括号内的地名信息
    name = re.sub(r'\([^)]*\)', '', name)
    
    # 去除地名前缀
    for prefix in location_prefixes:
        if name.startswith(prefix):
            name = name[len(prefix):]
            break
    
    # 去除公司类型后缀（按长度排序，先匹配长的）
    company_suffixes.sort(key=len, reverse=True)
    for suffix in company_suffixes:
        if name.endswith(suffix):
            name = name[:-len(suffix)]
            break
    
    # 如果处理后的名称太长，尝试提取核心词汇
    if len(name) > 6:
        # 尝试匹配常见的核心业务词汇模式
        business_patterns = [
            r'(\w{2,4})(化工|科技|实业|贸易|投资|建设|发展|制造|材料|设备|技术|服务|管理)',
            r'(\w{2,4})(集团|控股|股份)',
            r'(\w{2,4})(电子|机械|医药|食品|纺织|钢铁|有色|煤炭|石油|电力|交通|房地产)',
            r'(\w{2,4})(新能源|新材料|水性助剂|企业管理|资本管理|色彩科技)',
        ]
        
        for pattern in business_patterns:
            match = re.search(pattern, name)
            if match:
                return match.group(1) + match.group(2)
    
    # 如果名称仍然较长，取前4个字符
    if len(name) > 4:
        name = name[:4]
    
    # 如果处理后为空或太短，返回原名称的前几个字符
    if len(name) < 2:
        return full_name[:min(4, len(full_name))]
    
    return name

def remove_duplicate_contexts(contexts):
    """移除重复或高度相似的上下文片段"""
    if not contexts:
        return contexts
    
    print(f"    🔄 开始去重处理，原始上下文数量: {len(contexts)}")
    
    unique_contexts = []
    seen_texts = set()
    
    for i, context_info in enumerate(contexts):
        context_text = context_info['text']
        
        # 清理文本用于比较（移除多余空白和标点）
        cleaned_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', context_text)
        
        # 检查是否已经存在相同或高度相似的文本
        is_duplicate = False
        
        # 方法1：完全相同的清理后文本
        if cleaned_text in seen_texts:
            print(f"      ❌ 上下文 {i+1} 完全重复，跳过")
            is_duplicate = True
        else:
            # 方法2：检查高度相似的文本（包含关系）
            for seen_text in seen_texts:
                # 计算相似度：较短文本是否完全包含在较长文本中
                shorter_text = cleaned_text if len(cleaned_text) < len(seen_text) else seen_text
                longer_text = seen_text if len(cleaned_text) < len(seen_text) else cleaned_text
                
                # 如果较短文本完全包含在较长文本中，且长度差异不大，认为是重复
                if shorter_text in longer_text:
                    length_ratio = len(shorter_text) / len(longer_text) if len(longer_text) > 0 else 0
                    if length_ratio > 0.8:  # 80%以上相似度认为是重复
                        print(f"      ❌ 上下文 {i+1} 高度相似 (相似度: {length_ratio:.2f})，跳过")
                        is_duplicate = True
                        break
        
        if not is_duplicate:
            seen_texts.add(cleaned_text)
            unique_contexts.append(context_info)
            print(f"      ✅ 上下文 {i+1} 保留 (搜索词: {context_info.get('search_term', '未知')})")
    
    print(f"    📊 去重完成: {len(contexts)} -> {len(unique_contexts)} (移除 {len(contexts) - len(unique_contexts)} 个重复)")
    return unique_contexts

def test_extract_short_name():
    """测试公司简称提取"""
    print("🧪 测试公司简称提取功能")
    print("=" * 60)
    
    test_cases = [
        "常熟世名化工科技有限公司",
        "苏州汇彩新材料科技有限公司", 
        "昆山世盈资本管理有限公司",
        "苏州世润新材料科技有限公司",
        "苏州世名彩捷科技有限公司",
        "上海芯彩企业管理有限公司",
        "岳阳凯门水性助剂有限公司",
        "海南丹彩科技有限公司",
        "岳阳凯门新材料有限公司",
        "世名(苏州)新材料研究院有限公司",
        "世名(辽宁)新材料有限公司",
        "世名新能源科技(苏州)有限公司",
        "浙江上嘉色彩科技有限公司",
        "江苏锋晖新能源发展有限公司"
    ]
    
    print("测试结果:")
    for i, company in enumerate(test_cases, 1):
        short_name = extract_company_short_name(company)
        print(f"{i:2d}. {company} -> {short_name}")
    
    print("\n✅ 公司简称提取测试完成")
    return True

def test_duplicate_detection():
    """测试重复检测功能"""
    print("\n🧪 测试重复检测功能")
    print("=" * 60)
    
    # 创建测试上下文，包含重复内容
    test_contexts = [
        {
            'text': '公司与常熟世名化工科技有限公司建立了长期合作关系，在新材料研发方面开展深度合作。',
            'has_keywords': True,
            'keywords_found': ['合作', '新材料'],
            'search_term': '常熟世名化工科技有限公司',
            'is_short_name': False
        },
        {
            'text': '世名化工作为我们的重要供应商，提供高质量的化工原料。',
            'has_keywords': True,
            'keywords_found': ['供应商'],
            'search_term': '世名化工',
            'is_short_name': True
        },
        {
            'text': '公司与常熟世名化工科技有限公司建立了长期合作关系，在新材料研发方面开展深度合作。世名化工在行业中具有重要地位。',
            'has_keywords': True,
            'keywords_found': ['合作', '新材料'],
            'search_term': '常熟世名化工科技有限公司',
            'is_short_name': False
        },
        {
            'text': '苏州汇彩新材料科技有限公司是我们的战略合作伙伴。',
            'has_keywords': True,
            'keywords_found': ['合作'],
            'search_term': '苏州汇彩新材料科技有限公司',
            'is_short_name': False
        }
    ]
    
    print(f"原始上下文数量: {len(test_contexts)}")
    for i, ctx in enumerate(test_contexts, 1):
        print(f"  {i}. 搜索词: {ctx['search_term']} ({'简称' if ctx['is_short_name'] else '全名'})")
        print(f"     文本: {ctx['text'][:80]}...")
    
    # 执行去重
    unique_contexts = remove_duplicate_contexts(test_contexts)
    
    print(f"\n去重后上下文数量: {len(unique_contexts)}")
    for i, ctx in enumerate(unique_contexts, 1):
        print(f"  {i}. 搜索词: {ctx['search_term']} ({'简称' if ctx['is_short_name'] else '全名'})")
        print(f"     文本: {ctx['text'][:80]}...")
    
    print("\n✅ 重复检测测试完成")
    return True

if __name__ == "__main__":
    print("🚀 开始核心功能测试")
    print("=" * 80)
    
    success1 = test_extract_short_name()
    success2 = test_duplicate_detection()
    
    print("\n" + "=" * 80)
    if success1 and success2:
        print("🎉 所有测试通过")
    else:
        print("❌ 部分测试失败")
