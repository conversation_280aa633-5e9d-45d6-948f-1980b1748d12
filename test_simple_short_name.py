#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试公司简称提取功能
"""

import sys
import os
sys.path.append('docker-deploy')

def test_extract_short_name():
    """测试公司简称提取"""
    print("🧪 测试公司简称提取功能")
    print("=" * 60)
    
    try:
        from web_spider import extract_company_short_name
        
        test_cases = [
            "常熟世名化工科技有限公司",
            "苏州汇彩新材料科技有限公司", 
            "世名(苏州)新材料研究院有限公司",
            "世名(辽宁)新材料有限公司",
            "岳阳凯门水性助剂有限公司"
        ]
        
        print("测试结果:")
        for company in test_cases:
            short_name = extract_company_short_name(company)
            print(f"  {company} -> {short_name}")
        
        print("\n✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_duplicate_detection():
    """测试重复检测功能"""
    print("\n🧪 测试重复检测功能")
    print("=" * 60)
    
    try:
        from app import remove_duplicate_contexts
        
        # 创建测试上下文，包含重复内容
        test_contexts = [
            {
                'text': '公司与常熟世名化工科技有限公司建立了长期合作关系，在新材料研发方面开展深度合作。',
                'has_keywords': True,
                'keywords_found': ['合作', '新材料'],
                'search_term': '常熟世名化工科技有限公司',
                'is_short_name': False
            },
            {
                'text': '世名化工作为我们的重要供应商，提供高质量的化工原料。',
                'has_keywords': True,
                'keywords_found': ['供应商'],
                'search_term': '世名化工',
                'is_short_name': True
            },
            {
                'text': '公司与常熟世名化工科技有限公司建立了长期合作关系，在新材料研发方面开展深度合作。世名化工在行业中具有重要地位。',
                'has_keywords': True,
                'keywords_found': ['合作', '新材料'],
                'search_term': '常熟世名化工科技有限公司',
                'is_short_name': False
            },
            {
                'text': '苏州汇彩新材料科技有限公司是我们的战略合作伙伴。',
                'has_keywords': True,
                'keywords_found': ['合作'],
                'search_term': '苏州汇彩新材料科技有限公司',
                'is_short_name': False
            }
        ]
        
        print(f"原始上下文数量: {len(test_contexts)}")
        for i, ctx in enumerate(test_contexts, 1):
            print(f"  {i}. 搜索词: {ctx['search_term']} ({'简称' if ctx['is_short_name'] else '全名'})")
            print(f"     文本: {ctx['text'][:80]}...")
        
        # 执行去重
        unique_contexts = remove_duplicate_contexts(test_contexts)
        
        print(f"\n去重后上下文数量: {len(unique_contexts)}")
        for i, ctx in enumerate(unique_contexts, 1):
            print(f"  {i}. 搜索词: {ctx['search_term']} ({'简称' if ctx['is_short_name'] else '全名'})")
            print(f"     文本: {ctx['text'][:80]}...")
        
        print("\n✅ 重复检测测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始简单测试")
    print("=" * 80)
    
    success1 = test_extract_short_name()
    success2 = test_duplicate_detection()
    
    print("\n" + "=" * 80)
    if success1 and success2:
        print("🎉 所有测试通过")
    else:
        print("❌ 部分测试失败")
